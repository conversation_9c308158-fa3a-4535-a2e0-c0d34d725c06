from fastapi import <PERSON><PERSON><PERSON>, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.security import HTT<PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from motor.motor_asyncio import AsyncIOMotorClient
from datetime import datetime, timedelta
from jose import JWTError, jwt
from passlib.context import CryptContext
import pytz
import os
from dotenv import load_dotenv
from typing import Optional, List
from bson import ObjectId
import uvicorn

# Load environment variables
load_dotenv()

# Initialize FastAPI app
app = FastAPI(title="Tournament Management API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, specify your frontend domains
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Security
security = HTTPBearer()
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configuration
SECRET_KEY = os.getenv("SECRET_KEY")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_HOURS = 24
ADMIN_USERNAME = os.getenv("ADMIN_USERNAME")
ADMIN_PASSWORD = os.getenv("ADMIN_PASSWORD")

# MongoDB connection
MONGODB_URL = os.getenv("MONGODB_URL")
DATABASE_NAME = os.getenv("DATABASE_NAME")

# Global variables
client = None
database = None

# IST timezone
IST = pytz.timezone('Asia/Kolkata')

# Pydantic models will be imported from models.py
from models import *
from utils import *

@app.on_event("startup")
async def startup_db_client():
    global client, database
    client = AsyncIOMotorClient(MONGODB_URL)
    database = client[DATABASE_NAME]
    print("Connected to MongoDB")

@app.on_event("shutdown")
async def shutdown_db_client():
    if client:
        client.close()
        print("Disconnected from MongoDB")

# Authentication functions
def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

async def get_current_user(credentials: HTTPAuthorizationCredentials = Depends(security)):
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(credentials.credentials, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except JWTError:
        raise credentials_exception
    return username

# Routes
@app.get("/")
async def root():
    return {"message": "Tournament Management API", "status": "running"}

@app.post("/api/login")
async def login(login_data: LoginRequest):
    if login_data.username != ADMIN_USERNAME or login_data.password != ADMIN_PASSWORD:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password"
        )
    
    access_token_expires = timedelta(hours=ACCESS_TOKEN_EXPIRE_HOURS)
    access_token = create_access_token(
        data={"sub": login_data.username}, expires_delta=access_token_expires
    )
    return {"access_token": access_token, "token_type": "bearer", "expires_in": ACCESS_TOKEN_EXPIRE_HOURS * 3600}

# Tournament routes
@app.post("/api/tournaments")
async def create_tournament(tournament: TournamentCreate, current_user: str = Depends(get_current_user)):
    tournament_dict = tournament.model_dump()
    tournament_dict["created_at"] = datetime.utcnow()
    
    # Convert start_date to UTC if provided in IST
    if tournament_dict.get("start_date"):
        tournament_dict["start_date"] = convert_ist_to_utc(tournament_dict["start_date"])
    
    result = await database.tournaments.insert_one(tournament_dict)
    created_tournament = await database.tournaments.find_one({"_id": result.inserted_id})
    return convert_tournament_response(created_tournament)

@app.get("/api/tournaments/live")
async def get_live_tournament():
    tournament = await database.tournaments.find_one({"status": "live"})
    if not tournament:
        return None
    return convert_tournament_response(tournament)

@app.get("/api/tournaments/completed")
async def get_completed_tournaments():
    tournaments = []
    async for tournament in database.tournaments.find({"status": "completed"}):
        tournaments.append(convert_tournament_response(tournament))
    return tournaments

@app.patch("/api/tournaments/{tournament_id}/status")
async def update_tournament_status(tournament_id: str, status_update: StatusUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")
    
    # If setting to live, make sure no other tournament is live
    if status_update.status == "live":
        await database.tournaments.update_many(
            {"status": "live"},
            {"$set": {"status": "upcoming"}}
        )
    
    result = await database.tournaments.update_one(
        {"_id": ObjectId(tournament_id)},
        {"$set": {"status": status_update.status}}
    )
    
    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Tournament not found")
    
    updated_tournament = await database.tournaments.find_one({"_id": ObjectId(tournament_id)})
    return convert_tournament_response(updated_tournament)

# Team routes
@app.post("/api/teams")
async def create_team(team: TeamCreate, current_user: str = Depends(get_current_user)):
    team_dict = team.model_dump()

    # Generate initials if not provided
    if not team_dict.get("initials"):
        team_dict["initials"] = generate_initials(team_dict["name"])

    result = await database.teams.insert_one(team_dict)
    created_team = await database.teams.find_one({"_id": result.inserted_id})
    return convert_team_response(created_team)

@app.get("/api/tournaments/{tournament_id}/teams")
async def get_tournament_teams(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    teams = []
    async for team in database.teams.find({"tournament_id": tournament_id}):
        teams.append(convert_team_response(team))
    return teams

@app.patch("/api/teams/{team_id}")
async def update_team(team_id: str, team_update: TeamUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(team_id):
        raise HTTPException(status_code=400, detail="Invalid team ID")

    update_data = {k: v for k, v in team_update.model_dump().items() if v is not None}

    # Update initials if name is being updated
    if "name" in update_data and "initials" not in update_data:
        update_data["initials"] = generate_initials(update_data["name"])

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    result = await database.teams.update_one(
        {"_id": ObjectId(team_id)},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Team not found")

    updated_team = await database.teams.find_one({"_id": ObjectId(team_id)})
    return convert_team_response(updated_team)

@app.delete("/api/teams/{team_id}")
async def delete_team(team_id: str, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(team_id):
        raise HTTPException(status_code=400, detail="Invalid team ID")

    result = await database.teams.delete_one({"_id": ObjectId(team_id)})

    if result.deleted_count == 0:
        raise HTTPException(status_code=404, detail="Team not found")

    return {"message": "Team deleted successfully"}

# Match routes
@app.post("/api/matches")
async def create_match(match: MatchCreate, current_user: str = Depends(get_current_user)):
    match_dict = match.model_dump()

    # Convert start_time to UTC
    match_dict["start_time"] = convert_ist_to_utc(match_dict["start_time"])
    match_dict["status"] = "scheduled"

    result = await database.matches.insert_one(match_dict)
    created_match = await database.matches.find_one({"_id": result.inserted_id})
    return convert_match_response(created_match)

@app.get("/api/tournaments/{tournament_id}/matches")
async def get_tournament_matches(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    matches = []
    async for match in database.matches.find({"tournament_id": tournament_id}):
        matches.append(convert_match_response(match))
    return matches

@app.patch("/api/matches/{match_id}")
async def update_match(match_id: str, match_update: MatchUpdate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(match_id):
        raise HTTPException(status_code=400, detail="Invalid match ID")

    update_data = {k: v for k, v in match_update.model_dump().items() if v is not None}

    if not update_data:
        raise HTTPException(status_code=400, detail="No valid fields to update")

    result = await database.matches.update_one(
        {"_id": ObjectId(match_id)},
        {"$set": update_data}
    )

    if result.matched_count == 0:
        raise HTTPException(status_code=404, detail="Match not found")

    updated_match = await database.matches.find_one({"_id": ObjectId(match_id)})
    return convert_match_response(updated_match)

# Bracket routes
@app.post("/api/brackets/{tournament_id}")
async def create_bracket(tournament_id: str, bracket: BracketCreate, current_user: str = Depends(get_current_user)):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    bracket_dict = bracket.model_dump()
    bracket_dict["tournament_id"] = tournament_id

    result = await database.brackets.insert_one(bracket_dict)
    created_bracket = await database.brackets.find_one({"_id": result.inserted_id})
    return convert_bracket_response(created_bracket)

@app.get("/api/brackets/{tournament_id}")
async def get_tournament_brackets(tournament_id: str):
    if not ObjectId.is_valid(tournament_id):
        raise HTTPException(status_code=400, detail="Invalid tournament ID")

    brackets = []
    async for bracket in database.brackets.find({"tournament_id": tournament_id}).sort("round", 1):
        brackets.append(convert_bracket_response(bracket))
    return brackets

if __name__ == "__main__":
    uvicorn.run("main:app", host="0.0.0.0", port=int(os.getenv("PORT", 8000)), reload=True)
