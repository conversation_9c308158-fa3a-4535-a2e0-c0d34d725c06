<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tournament Admin Panel</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <!-- Login Screen -->
    <div id="loginScreen" class="screen">
        <div class="login-container">
            <h1>Tournament Admin Panel</h1>
            <form id="loginForm">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <button type="submit">Login</button>
            </form>
            <div id="loginError" class="error-message"></div>
        </div>
    </div>

    <!-- Dashboard Screen -->
    <div id="dashboardScreen" class="screen hidden">
        <nav class="navbar">
            <h1>Tournament Admin Panel</h1>
            <div class="nav-actions">
                <span id="welcomeUser"></span>
                <button id="logoutBtn" class="btn-secondary">Logout</button>
            </div>
        </nav>

        <div class="container">
            <div class="tabs">
                <button class="tab-btn active" data-tab="tournaments">Tournaments</button>
                <button class="tab-btn" data-tab="teams">Teams</button>
                <button class="tab-btn" data-tab="matches">Matches</button>
                <button class="tab-btn" data-tab="brackets">Brackets</button>
            </div>

            <!-- Tournaments Tab -->
            <div id="tournaments" class="tab-content active">
                <div class="section-header">
                    <h2>Tournaments</h2>
                    <div>
                        <button id="refreshTournamentsBtn" class="btn-secondary">Refresh</button>
                        <button id="addTournamentBtn" class="btn-primary">Add Tournament</button>
                    </div>
                </div>
                <div id="tournamentsList" class="data-list"></div>
            </div>

            <!-- Teams Tab -->
            <div id="teams" class="tab-content">
                <div class="section-header">
                    <h2>Teams</h2>
                    <select id="teamTournamentSelect" class="tournament-select">
                        <option value="">Select Tournament</option>
                    </select>
                    <button id="addTeamBtn" class="btn-primary">Add Team</button>
                </div>
                <div id="teamsList" class="data-list"></div>
            </div>

            <!-- Matches Tab -->
            <div id="matches" class="tab-content">
                <div class="section-header">
                    <h2>Matches</h2>
                    <select id="matchTournamentSelect" class="tournament-select">
                        <option value="">Select Tournament</option>
                    </select>
                    <button id="addMatchBtn" class="btn-primary">Add Match</button>
                </div>
                <div id="matchesList" class="data-list"></div>
            </div>

            <!-- Brackets Tab -->
            <div id="brackets" class="tab-content">
                <div class="section-header">
                    <h2>Brackets</h2>
                    <select id="bracketTournamentSelect" class="tournament-select">
                        <option value="">Select Tournament</option>
                    </select>
                    <button id="addBracketBtn" class="btn-primary">Add Bracket</button>
                </div>
                <div id="bracketsList" class="data-list"></div>
            </div>
        </div>
    </div>

    <!-- Modal for forms -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <span class="close">&times;</span>
            <div id="modalBody"></div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
