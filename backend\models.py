from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime
from bson import ObjectId

class PyObjectId(ObjectId):
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")

# Authentication models
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int

# Tournament models
class TournamentCreate(BaseModel):
    name: str
    type: str = "Single Elimination"
    start_date: Optional[datetime] = None
    prize_pool: Optional[int] = 0
    status: str = "upcoming"

class TournamentResponse(BaseModel):
    id: str = Field(alias="_id")
    name: str
    type: str
    start_date: Optional[datetime] = None
    prize_pool: Optional[int] = 0
    status: str
    created_at: datetime

    class Config:
        populate_by_name = True

class StatusUpdate(BaseModel):
    status: str

# Team models
class TeamCreate(BaseModel):
    tournament_id: str
    name: str
    members: List[str]
    icon: Optional[str] = None
    initials: Optional[str] = None

class TeamResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    name: str
    members: List[str]
    icon: Optional[str] = None
    initials: Optional[str] = None

    class Config:
        populate_by_name = True

class TeamUpdate(BaseModel):
    name: Optional[str] = None
    members: Optional[List[str]] = None
    icon: Optional[str] = None
    initials: Optional[str] = None

# Match models
class MatchCreate(BaseModel):
    tournament_id: str
    teamA: str
    teamB: str
    start_time: datetime
    match_id: Optional[str] = None

class MatchResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    teamA: str
    teamB: str
    start_time: datetime
    status: str = "scheduled"
    scoreA: Optional[int] = None
    scoreB: Optional[int] = None
    winner: Optional[str] = None
    match_id: Optional[str] = None

    class Config:
        populate_by_name = True

class MatchUpdate(BaseModel):
    status: Optional[str] = None
    scoreA: Optional[int] = None
    scoreB: Optional[int] = None
    winner: Optional[str] = None

# Bracket models
class Matchup(BaseModel):
    teamA: Optional[str] = None
    teamB: Optional[str] = None
    winner: Optional[str] = None

class BracketCreate(BaseModel):
    tournament_id: str
    round: int
    matchups: List[Matchup]

class BracketResponse(BaseModel):
    id: str = Field(alias="_id")
    tournament_id: str
    round: int
    matchups: List[Matchup]

    class Config:
        populate_by_name = True
