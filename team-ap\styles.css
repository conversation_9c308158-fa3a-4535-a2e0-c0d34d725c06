/* Valorant-inspired CSS Variables */
:root {
    --valorant-red: #ff4655;
    --valorant-blue: #0f1419;
    --valorant-dark: #0f1419;
    --valorant-darker: #0a0e13;
    --valorant-light: #ece8e1;
    --valorant-gold: #f0d343;
    --valorant-cyan: #00d4ff;
    --valorant-purple: #9146ff;
    --valorant-green: #00ff88;
    
    --gradient-primary: linear-gradient(135deg, var(--valorant-red) 0%, var(--valorant-purple) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--valorant-cyan) 0%, var(--valorant-blue) 100%);
    --gradient-accent: linear-gradient(135deg, var(--valorant-gold) 0%, var(--valorant-red) 100%);
    
    --font-primary: 'Raj<PERSON><PERSON>', sans-serif;
    --font-weight-light: 300;
    --font-weight-normal: 400;
    --font-weight-medium: 500;
    --font-weight-semibold: 600;
    --font-weight-bold: 700;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: var(--font-primary);
    background-color: var(--valorant-dark);
    color: var(--valorant-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(15, 20, 25, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    border-bottom: 2px solid var(--valorant-red);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h1 {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
}

.nav-logo .tagline {
    font-size: 0.8rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
    letter-spacing: 1px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--valorant-light);
    text-decoration: none;
    font-weight: var(--font-weight-semibold);
    font-size: 1.1rem;
    letter-spacing: 1px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--valorant-red);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: width 0.3s ease;
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger .bar {
    width: 25px;
    height: 3px;
    background: var(--valorant-red);
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('https://images.unsplash.com/photo-1542751371-adc38448a05e?ixlib=rb-4.0.3') center/cover;
    z-index: -2;
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(15, 20, 25, 0.8) 0%, rgba(255, 70, 85, 0.3) 100%);
    z-index: -1;
}

.hero-content {
    text-align: center;
    z-index: 1;
}

.hero-title {
    font-size: 4rem;
    font-weight: var(--font-weight-bold);
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 2rem;
    letter-spacing: 3px;
    text-shadow: 0 0 30px rgba(255, 70, 85, 0.5);
}

.tournament-info {
    background: rgba(15, 20, 25, 0.9);
    padding: 2rem;
    border-radius: 15px;
    border: 2px solid var(--valorant-red);
    backdrop-filter: blur(10px);
    max-width: 600px;
    margin: 0 auto;
}

/* Section Titles */
.section-title {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    text-align: center;
    margin-bottom: 3rem;
    background: var(--gradient-accent);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 2px;
}

/* Recent Matches */
.recent-matches {
    padding: 5rem 0;
    background: var(--valorant-darker);
}

.matches-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.match-card {
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1) 0%, rgba(145, 70, 255, 0.1) 100%);
    border: 2px solid var(--valorant-red);
    border-radius: 15px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.match-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.match-card:hover::before {
    left: 100%;
}

.match-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(255, 70, 85, 0.3);
}

.match-teams {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.team {
    text-align: center;
    flex: 1;
}

.team-name {
    font-size: 1.3rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
    margin-bottom: 0.5rem;
}

.team-score {
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-gold);
}

.vs {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-red);
    margin: 0 1rem;
}

.match-info {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(255, 70, 85, 0.3);
}

.match-time {
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
}

.match-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: var(--font-weight-semibold);
    font-size: 0.9rem;
    margin-top: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.match-status.completed {
    background: var(--gradient-accent);
    color: var(--valorant-dark);
}

.match-status.live {
    background: var(--valorant-green);
    color: var(--valorant-dark);
    animation: pulse 2s infinite;
}

.match-status.scheduled {
    background: var(--gradient-secondary);
    color: var(--valorant-light);
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}

/* Tournament Details */
.tournament-details {
    padding: 5rem 0;
}

.tournament-card {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(15, 20, 25, 0.9) 100%);
    border: 2px solid var(--valorant-cyan);
    border-radius: 20px;
    padding: 3rem;
    text-align: center;
}

.tournament-name {
    font-size: 3rem;
    font-weight: var(--font-weight-bold);
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.tournament-type {
    font-size: 1.5rem;
    color: var(--valorant-gold);
    margin-bottom: 2rem;
    font-weight: var(--font-weight-medium);
}

.tournament-prize {
    font-size: 2rem;
    color: var(--valorant-green);
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
}

.tournament-status {
    display: inline-block;
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: var(--font-weight-bold);
    font-size: 1.2rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.tournament-status.live {
    background: var(--gradient-primary);
    color: var(--valorant-light);
    animation: glow 2s infinite alternate;
}

.tournament-status.upcoming {
    background: var(--gradient-secondary);
    color: var(--valorant-light);
}

.tournament-status.completed {
    background: var(--gradient-accent);
    color: var(--valorant-dark);
}

@keyframes glow {
    from { box-shadow: 0 0 20px rgba(255, 70, 85, 0.5); }
    to { box-shadow: 0 0 30px rgba(255, 70, 85, 0.8); }
}

/* Loading Animation */
.loading {
    text-align: center;
    font-size: 1.2rem;
    color: var(--valorant-cyan);
    padding: 2rem;
    animation: pulse 1.5s infinite;
}

/* Footer */
.footer {
    background: var(--valorant-darker);
    border-top: 2px solid var(--valorant-red);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3 {
    color: var(--valorant-red);
    font-size: 1.3rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1rem;
    letter-spacing: 1px;
}

.footer-section p {
    color: var(--valorant-light);
    line-height: 1.6;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--valorant-light);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: var(--valorant-cyan);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid rgba(255, 70, 85, 0.3);
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
}

/* Page Header */
.page-header {
    padding: 8rem 0 4rem;
    background: var(--gradient-primary);
    text-align: center;
}

.page-title {
    font-size: 3.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
    margin-bottom: 1rem;
    letter-spacing: 3px;
}

.page-subtitle {
    font-size: 1.3rem;
    color: var(--valorant-light);
    opacity: 0.9;
    font-weight: var(--font-weight-medium);
}

/* Tournament Selector */
.tournament-selector {
    padding: 2rem 0;
    background: var(--valorant-darker);
}

.tournament-info-bar {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(15, 20, 25, 0.9) 100%);
    border: 2px solid var(--valorant-cyan);
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
}

.tournament-info-content .tournament-name {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.tournament-details {
    display: flex;
    justify-content: center;
    gap: 2rem;
    flex-wrap: wrap;
}

.tournament-details span {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: var(--font-weight-semibold);
    font-size: 1rem;
}

.tournament-type {
    background: var(--gradient-accent);
    color: var(--valorant-dark);
}

.tournament-prize {
    background: var(--valorant-green);
    color: var(--valorant-dark);
}

/* Schedule Content */
.schedule-content {
    padding: 3rem 0 5rem;
}

/* Filter Tabs */
.filter-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--valorant-red);
    color: var(--valorant-red);
    padding: 1rem 2rem;
    border-radius: 30px;
    font-weight: var(--font-weight-semibold);
    font-size: 1rem;
    letter-spacing: 1px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--gradient-primary);
    color: var(--valorant-light);
    border-color: transparent;
}

/* Schedule Match Cards */
.matches-list {
    display: grid;
    gap: 2rem;
}

.schedule-match-card {
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.05) 0%, rgba(145, 70, 255, 0.05) 100%);
    border: 2px solid var(--valorant-red);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.schedule-match-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(255, 70, 85, 0.2);
}

.match-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(255, 70, 85, 0.2);
}

.match-id {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-cyan);
}

.schedule-match-card .match-teams {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 2rem;
    align-items: center;
    margin-bottom: 1rem;
}

.schedule-match-card .team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
}

.schedule-match-card .team.winner {
    background: linear-gradient(135deg, rgba(240, 211, 67, 0.2) 0%, rgba(255, 70, 85, 0.2) 100%);
    border: 1px solid var(--valorant-gold);
}

.team-info {
    text-align: left;
}

.schedule-match-card .team-name {
    font-size: 1.3rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
    margin-bottom: 0.5rem;
}

.team-initials {
    font-size: 0.9rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
}

.schedule-match-card .team-score {
    font-size: 2.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-gold);
    min-width: 60px;
    text-align: center;
}

.vs-section {
    text-align: center;
}

.schedule-match-card .vs {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-red);
    margin-bottom: 0.5rem;
}

.schedule-match-card .match-time {
    font-size: 0.9rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
}

.match-result {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 70, 85, 0.2);
}

.winner-announcement {
    font-size: 1.2rem;
    color: var(--valorant-light);
    font-weight: var(--font-weight-medium);
}

.winner-name {
    color: var(--valorant-gold);
    font-weight: var(--font-weight-bold);
}

/* No Data Message */
.no-data {
    text-align: center;
    padding: 3rem;
    font-size: 1.3rem;
    color: var(--valorant-cyan);
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 2px dashed var(--valorant-cyan);
}

.no-tournament {
    text-align: center;
    padding: 3rem;
}

.no-tournament h2 {
    font-size: 2rem;
    color: var(--valorant-red);
    margin-bottom: 1rem;
}

.no-tournament p {
    font-size: 1.2rem;
    color: var(--valorant-light);
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--valorant-dark);
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .tournament-info {
        margin: 0 1rem;
        padding: 1.5rem;
    }
    
    .container {
        padding: 0 1rem;
    }
    
    .matches-grid {
        grid-template-columns: 1fr;
    }
    
    .tournament-name {
        font-size: 2rem;
    }
    
    .section-title {
        font-size: 2rem;
    }

    .page-title {
        font-size: 2.5rem;
    }

    .tournament-details {
        flex-direction: column;
        gap: 1rem;
    }

    .filter-tabs {
        flex-direction: column;
        align-items: center;
    }

    .filter-btn {
        width: 200px;
    }

    .schedule-match-card .match-teams {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .vs-section {
        order: -1;
    }
}

/* Brackets Styles */
.brackets-content {
    padding: 3rem 0 5rem;
}

.brackets-container {
    overflow-x: auto;
    padding: 1rem 0;
}

.brackets-wrapper {
    display: flex;
    gap: 3rem;
    min-width: max-content;
    padding: 1rem;
}

.bracket-round {
    min-width: 300px;
}

.round-title {
    font-size: 1.8rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-red);
    text-align: center;
    margin-bottom: 2rem;
    padding: 1rem;
    background: linear-gradient(135deg, rgba(255, 70, 85, 0.1) 0%, rgba(145, 70, 255, 0.1) 100%);
    border-radius: 15px;
    border: 2px solid var(--valorant-red);
}

.matchups-container {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.bracket-matchup {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.05) 0%, rgba(15, 20, 25, 0.9) 100%);
    border: 2px solid var(--valorant-cyan);
    border-radius: 15px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.bracket-matchup:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 212, 255, 0.2);
}

.matchup-header {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-cyan);
    text-align: center;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.matchup-teams {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.bracket-team {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    transition: all 0.3s ease;
}

.bracket-team.winner {
    background: linear-gradient(135deg, rgba(240, 211, 67, 0.2) 0%, rgba(255, 70, 85, 0.2) 100%);
    border: 1px solid var(--valorant-gold);
}

.bracket-team .team-name {
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
}

.bracket-team .team-initials {
    font-size: 0.9rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
    background: rgba(0, 212, 255, 0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
}

.vs-divider {
    text-align: center;
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-red);
    margin: 0.5rem 0;
}

.matchup-winner {
    text-align: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid rgba(0, 212, 255, 0.3);
    font-size: 1.1rem;
    color: var(--valorant-gold);
    font-weight: var(--font-weight-bold);
}

.no-brackets {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 2px dashed var(--valorant-cyan);
}

.no-brackets h2 {
    font-size: 2.5rem;
    color: var(--valorant-red);
    margin-bottom: 1rem;
}

.no-brackets p {
    font-size: 1.3rem;
    color: var(--valorant-light);
}

/* History Styles */
.history-content {
    padding: 3rem 0 5rem;
}

.history-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.history-card {
    background: linear-gradient(135deg, rgba(240, 211, 67, 0.05) 0%, rgba(255, 70, 85, 0.05) 100%);
    border: 2px solid var(--valorant-gold);
    border-radius: 20px;
    padding: 2rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.history-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(240, 211, 67, 0.2);
}

.history-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid rgba(240, 211, 67, 0.3);
}

.history-card .tournament-name {
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
    margin: 0;
}

.tournament-date {
    font-size: 0.9rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
}

.tournament-info {
    margin-bottom: 1.5rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.8rem;
}

.info-label {
    color: var(--valorant-light);
    font-weight: var(--font-weight-medium);
}

.info-value {
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-semibold);
}

.info-value.prize {
    color: var(--valorant-green);
}

.history-card .tournament-status {
    display: inline-block;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-weight: var(--font-weight-bold);
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 1.5rem;
}

.card-actions {
    text-align: center;
}

.view-details-btn {
    background: var(--gradient-accent);
    color: var(--valorant-dark);
    border: none;
    padding: 0.8rem 2rem;
    border-radius: 25px;
    font-weight: var(--font-weight-bold);
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 1px;
}

.view-details-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(240, 211, 67, 0.4);
}

.no-history {
    text-align: center;
    padding: 4rem 2rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 20px;
    border: 2px dashed var(--valorant-gold);
}

.no-history h2 {
    font-size: 2.5rem;
    color: var(--valorant-red);
    margin-bottom: 1rem;
}

.no-history p {
    font-size: 1.3rem;
    color: var(--valorant-light);
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    padding: 2rem;
}

.modal-content {
    background: var(--valorant-dark);
    border: 2px solid var(--valorant-red);
    border-radius: 20px;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
}

.tournament-details-modal {
    padding: 0;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 2rem;
    border-bottom: 2px solid var(--valorant-red);
    background: var(--gradient-primary);
}

.modal-header h2 {
    color: var(--valorant-light);
    font-size: 2rem;
    font-weight: var(--font-weight-bold);
    margin: 0;
}

.close-modal {
    color: var(--valorant-light);
    font-size: 2rem;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-modal:hover {
    color: var(--valorant-gold);
}

.modal-body {
    padding: 2rem;
}

.tournament-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.summary-item {
    text-align: center;
    padding: 1.5rem;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    border: 1px solid rgba(255, 70, 85, 0.3);
}

.summary-item h3 {
    color: var(--valorant-red);
    font-size: 1.1rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.summary-item p {
    color: var(--valorant-light);
    font-size: 1.2rem;
    font-weight: var(--font-weight-semibold);
}

.summary-item p.prize {
    color: var(--valorant-green);
    font-size: 1.5rem;
}

.summary-item p.winner {
    color: var(--valorant-gold);
    font-size: 1.3rem;
}

.teams-section {
    margin-top: 2rem;
}

.teams-section h3 {
    color: var(--valorant-cyan);
    font-size: 1.5rem;
    font-weight: var(--font-weight-bold);
    margin-bottom: 1.5rem;
    text-align: center;
}

.teams-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.team-card {
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(15, 20, 25, 0.9) 100%);
    border: 2px solid var(--valorant-cyan);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
}

.team-card.champion {
    background: linear-gradient(135deg, rgba(240, 211, 67, 0.2) 0%, rgba(255, 70, 85, 0.2) 100%);
    border-color: var(--valorant-gold);
    transform: scale(1.05);
}

.team-card .team-name {
    font-size: 1.2rem;
    font-weight: var(--font-weight-bold);
    color: var(--valorant-light);
    margin-bottom: 0.5rem;
}

.team-card .team-initials {
    font-size: 1rem;
    color: var(--valorant-cyan);
    font-weight: var(--font-weight-medium);
    background: rgba(0, 212, 255, 0.2);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    display: inline-block;
}

.champion-badge {
    position: absolute;
    top: -10px;
    right: -10px;
    background: var(--gradient-accent);
    color: var(--valorant-dark);
    padding: 0.3rem 0.8rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: var(--font-weight-bold);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}
